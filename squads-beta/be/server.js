const Koa = require('koa');
const Router = require('koa-router');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

const app = new Koa();
const router = new Router();

// 配置 - 改为主网
const SOLANA_RPC_URL = 'https://api.mainnet-beta.solana.com';
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

// 创建连接
const connection = new Connection(SOLANA_RPC_URL, 'confirmed');

// 中间件
app.use(cors({
  origin: '*',
  credentials: true,
  'Access-Control-Allow-Origin': true,
  'Access-Control-Allow-Methods': true,
  'Access-Control-Allow-Headers': true,
}));

app.use(bodyParser());

// 健康检查
router.get('/health', async (ctx) => {
  ctx.body = { status: 'ok', timestamp: new Date().toISOString(), network: 'mainnet-beta' };
});

// 获取多签账户 - 修复查询方法
router.post('/api/multisig/accounts', async (ctx) => {
  try {
    const { publicKey } = ctx.request.body;

    if (!publicKey) {
      ctx.status = 400;
      ctx.body = { error: '缺少publicKey参数' };
      return;
    }

    const userPublicKey = new PublicKey(publicKey);

    // 由于主网限制了getProgramAccounts，我们需要使用其他方法
    // 这里可以存储一些已知的多签账户地址，或者让用户提供
    const multisigs = [];

    // 如果有已知的多签账户地址，可以直接查询
    const knownMultisigAddresses = [
      'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr', // 用户提供的多签地址
      // 可以添加更多已知地址
    ];

    for (const address of knownMultisigAddresses) {
      try {
        const multisigPubkey = new PublicKey(address);
        const multisigData = await multisig.accounts.Multisig.fromAccountAddress(
          connection,
          multisigPubkey
        );

        // 检查当前钱包是否是成员
        const isMember = multisigData.members.some(member =>
          member.key.equals(userPublicKey)
        );

        if (isMember) {
          multisigs.push({
            address: address,
            members: multisigData.members.length,
            threshold: multisigData.threshold,
            transactionIndex: Number(multisigData.transactionIndex.toString()),
          });
        }
      } catch (err) {
        console.warn('Failed to parse multisig account:', address, err.message);
      }
    }

    ctx.body = { multisigs };
  } catch (error) {
    console.error('获取多签账户失败:', error);
    ctx.status = 500;
    ctx.body = { error: '获取多签账户失败' };
  }
});

// 新增：通过多签地址查询详情
router.post('/api/multisig/details', async (ctx) => {
  try {
    const { multisigAddress } = ctx.request.body;

    if (!multisigAddress) {
      ctx.status = 400;
      ctx.body = { error: '缺少multisigAddress参数' };
      return;
    }

    const multisigPubkey = new PublicKey(multisigAddress);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(
      connection,
      multisigPubkey
    );

    const result = {
      address: multisigAddress,
      members: multisigData.members.map(member => ({
        key: member.key.toBase58(),
        permissions: member.permissions
      })),
      threshold: multisigData.threshold,
      transactionIndex: Number(multisigData.transactionIndex.toString()),
      createKey: multisigData.createKey.toBase58(),
      allowExternalExecute: multisigData.allowExternalExecute,
      rentCollector: multisigData.rentCollector?.toBase58(),
    };

    ctx.body = result;
  } catch (error) {
    console.error('获取多签详情失败:', error);
    ctx.status = 500;
    ctx.body = { error: '获取多签详情失败' };
  }
});

// 获取账户余额
router.post('/api/solana/balance', async (ctx) => {
  try {
    const { publicKey } = ctx.request.body;

    if (!publicKey) {
      ctx.status = 400;
      ctx.body = { error: '缺少publicKey参数' };
      return;
    }

    const pubkey = new PublicKey(publicKey);
    const balance = await connection.getBalance(pubkey);

    ctx.body = { balance };
  } catch (error) {
    console.error('获取余额失败:', error);
    ctx.status = 500;
    ctx.body = { error: '获取余额失败' };
  }
});

// 获取最新区块哈希
router.get('/api/solana/latest-blockhash', async (ctx) => {
  try {
    const { blockhash } = await connection.getLatestBlockhash();
    ctx.body = { blockhash };
  } catch (error) {
    console.error('获取区块哈希失败:', error);
    ctx.status = 500;
    ctx.body = { error: '获取区块哈希失败' };
  }
});

// 发送交易
router.post('/api/solana/send-transaction', async (ctx) => {
  try {
    const { transaction } = ctx.request.body;

    if (!transaction) {
      ctx.status = 400;
      ctx.body = { error: '缺少transaction参数' };
      return;
    }

    const signature = await connection.sendRawTransaction(
      Buffer.from(transaction, 'base64')
    );

    ctx.body = { signature };
  } catch (error) {
    console.error('发送交易失败:', error);
    ctx.status = 500;
    ctx.body = { error: '发送交易失败' };
  }
});

// 确认交易
router.post('/api/solana/confirm-transaction', async (ctx) => {
  try {
    const { signature } = ctx.request.body;

    if (!signature) {
      ctx.status = 400;
      ctx.body = { error: '缺少signature参数' };
      return;
    }

    const result = await connection.confirmTransaction(signature);
    ctx.body = { result };
  } catch (error) {
    console.error('确认交易失败:', error);
    ctx.status = 500;
    ctx.body = { error: '确认交易失败' };
  }
});

// 通用RPC代理
router.post('/api/solana/rpc', async (ctx) => {
  try {
    const { method, params } = ctx.request.body;

    if (!method) {
      ctx.status = 400;
      ctx.body = { error: '缺少method参数' };
      return;
    }

    const result = await connection._rpcRequest(method, params || []);
    ctx.body = result;
  } catch (error) {
    console.error('RPC调用失败:', error);
    ctx.status = 500;
    ctx.body = { error: 'RPC调用失败' };
  }
});

// 创建多签SOL转账交易
router.post('/api/multisig/create-sol-transfer', async (ctx) => {
  try {
    const {
      multisigAddress,
      recipientAddress,
      amount,
      creatorPublicKey,
      signedTransaction
    } = ctx.request.body;

    if (!multisigAddress || !recipientAddress || !amount || !creatorPublicKey || !signedTransaction) {
      ctx.status = 400;
      ctx.body = { error: '缺少必要参数' };
      return;
    }

    console.log(`处理SOL转账: ${amount} SOL to ${recipientAddress.substring(0, 8)}...`);

    // 先进行交易模拟验证
    try {
      const transactionBuffer = Buffer.from(signedTransaction, 'base64');
      console.log('模拟交易验证...');

      const simulateResult = await connection.simulateTransaction(
        transactionBuffer,
        {
          commitment: 'confirmed',
          sigVerify: false
        }
      );

      if (simulateResult.value.err) {
        console.error('交易模拟失败:', simulateResult.value.err);
        throw new Error(`交易模拟失败: ${JSON.stringify(simulateResult.value.err)}`);
      }

      console.log('交易模拟成功');
    } catch (simulateError) {
      console.error('模拟验证出错:', simulateError);
      throw new Error(`交易验证失败: ${simulateError.message}`);
    }

    // 发送已签名的交易
    console.log('发送交易到链上...');
    const signature = await connection.sendRawTransaction(
      Buffer.from(signedTransaction, 'base64'),
      {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
        maxRetries: 3
      }
    );

    console.log(`交易已发送，签名: ${signature}`);

    // 等待交易确认
    const result = await connection.confirmTransaction(signature, 'confirmed');
    console.log('交易确认结果:', result);

    ctx.body = {
      signature,
      result,
      success: true,
      message: `SOL转账交易创建成功: ${amount} SOL to ${recipientAddress.substring(0, 8)}...`
    };
  } catch (error) {
    console.error('创建多签转账失败:', error);

    let errorMessage = '创建多签转账失败: ' + error.message;

    // 提供更友好的错误信息
    if (error.message?.includes('insufficient funds')) {
      errorMessage = '账户余额不足，请检查金库余额和交易费用';
    } else if (error.message?.includes('Transaction simulation failed')) {
      errorMessage = '交易模拟失败，请检查转账参数和账户状态';
    } else if (error.message?.includes('Blockhash not found')) {
      errorMessage = '区块哈希过期，请重新发起交易';
    }

    ctx.status = 500;
    ctx.body = { error: errorMessage };
  }
});

// 获取多签交易信息
router.post('/api/multisig/get-vault-info', async (ctx) => {
  try {
    const { multisigAddress } = ctx.request.body;

    if (!multisigAddress) {
      ctx.status = 400;
      ctx.body = { error: '缺少multisigAddress参数' };
      return;
    }

    const multisigPubkey = new PublicKey(multisigAddress);

    // 获取多签信息
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(
      connection,
      multisigPubkey
    );

    // 获取金库地址和余额
    const [vaultPda] = multisig.getVaultPda({
      multisigPda: multisigPubkey,
      index: 0,
      programId: MULTISIG_PROGRAM_ID
    });

    const vaultBalance = await connection.getBalance(vaultPda);

    ctx.body = {
      multisigInfo: {
        address: multisigAddress,
        members: multisigData.members.map(member => ({
          key: member.key.toBase58(),
          permissions: member.permissions
        })),
        threshold: multisigData.threshold,
        transactionIndex: Number(multisigData.transactionIndex.toString()),
      },
      vaultInfo: {
        address: vaultPda.toBase58(),
        balance: vaultBalance,
        balanceSOL: vaultBalance / 1e9
      }
    };
  } catch (error) {
    console.error('获取金库信息失败:', error);
    ctx.status = 500;
    ctx.body = { error: '获取金库信息失败: ' + error.message };
  }
});

// 获取Token账户余额
router.post('/api/solana/token-balance', async (ctx) => {
  try {
    const { tokenAccount } = ctx.request.body;

    if (!tokenAccount) {
      ctx.status = 400;
      ctx.body = { error: '缺少tokenAccount参数' };
      return;
    }

    const tokenAccountPubkey = new PublicKey(tokenAccount);

    // 获取Token账户信息
    const tokenAccountInfo = await connection.getTokenAccountBalance(tokenAccountPubkey);

    ctx.body = {
      balance: parseInt(tokenAccountInfo.value.amount),
      decimals: tokenAccountInfo.value.decimals,
      uiAmount: tokenAccountInfo.value.uiAmount
    };
  } catch (error) {
    console.error('获取Token余额失败:', error);
    // 如果账户不存在，返回0余额
    ctx.body = {
      balance: 0,
      decimals: 0,
      uiAmount: 0
    };
  }
});

// 创建多签Token转账交易
router.post('/api/multisig/create-token-transfer', async (ctx) => {
  try {
    const {
      multisigAddress,
      recipientAddress,
      tokenMint,
      amount,
      decimals,
      creatorPublicKey,
      signedTransaction
    } = ctx.request.body;

    if (!multisigAddress || !recipientAddress || !tokenMint || !amount || !decimals || !creatorPublicKey || !signedTransaction) {
      ctx.status = 400;
      ctx.body = { error: '缺少必要参数' };
      return;
    }

    // 发送已签名的交易
    const signature = await connection.sendRawTransaction(
      Buffer.from(signedTransaction, 'base64')
    );

    // 等待交易确认
    const result = await connection.confirmTransaction(signature);

    ctx.body = {
      signature,
      result,
      success: true
    };
  } catch (error) {
    console.error('创建多签Token转账失败:', error);
    ctx.status = 500;
    ctx.body = { error: '创建多签Token转账失败: ' + error.message };
  }
});

// 获取多签提案列表
router.post('/api/multisig/proposals', async (ctx) => {
  try {
    const { multisigAddress } = ctx.request.body;

    if (!multisigAddress) {
      ctx.status = 400;
      ctx.body = { error: '缺少multisigAddress参数' };
      return;
    }

    const multisigPubkey = new PublicKey(multisigAddress);

    // 获取多签信息
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(
      connection,
      multisigPubkey
    );

    const proposals = [];
    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());

    // 查询最近的提案 (假设查询最近20个)
    const maxProposalsToCheck = 20;
    const startIndex = Math.max(1, currentTransactionIndex - maxProposalsToCheck);

    for (let i = startIndex; i <= currentTransactionIndex; i++) {
      try {
        // 获取提案PDA
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        // 尝试获取提案账户
        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (!proposalAccount) continue;

        // 解析提案数据
        const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

        // 获取交易数据
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const transactionAccount = await connection.getAccountInfo(transactionPda);
        let memo = '';
        if (transactionAccount) {
          try {
            const transactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
            memo = transactionData.memo || '';
          } catch (err) {
            // 忽略解析错误
          }
        }

        // 统计投票
        const approvals = proposalData.approved.length;
        const rejections = proposalData.rejected.length;
        const threshold = multisigData.threshold;

        // 修复状态检查 - 使用__kind属性
        let status = 'Active';
        if (proposalData.status.__kind === 'Executed') {
          status = 'Executed';
        } else if (proposalData.status.__kind === 'Rejected') {
          status = 'Rejected';
        } else if (proposalData.status.__kind === 'Cancelled') {
          status = 'Cancelled';
        } else if (approvals >= threshold) {
          status = 'Approved';
        }

        // 构建投票信息
        const votes = [];
        proposalData.approved.forEach(member => {
          votes.push({
            member: member.toBase58(),
            vote: 'Approve'
          });
        });
        proposalData.rejected.forEach(member => {
          votes.push({
            member: member.toBase58(),
            vote: 'Reject'
          });
        });

        // 修复creator字段处理
        const creatorAddress = proposalData.creator ? proposalData.creator.toBase58() : 'Unknown';

        proposals.push({
          transactionIndex: i,
          status,
          approvals,
          threshold,
          creator: creatorAddress,
          memo,
          votes,
          canExecute: status === 'Approved'
        });

      } catch (err) {
        // 跳过解析失败的提案
        console.warn(`Failed to parse proposal ${i}:`, err.message);
        continue;
      }
    }

    // 按交易索引倒序排列
    proposals.sort((a, b) => b.transactionIndex - a.transactionIndex);

    ctx.body = { proposals };
  } catch (error) {
    console.error('获取提案失败:', error);
    ctx.status = 500;
    ctx.body = { error: '获取提案失败: ' + error.message };
  }
});

// 提交投票
router.post('/api/multisig/vote', async (ctx) => {
  try {
    const {
      multisigAddress,
      transactionIndex,
      vote,
      voterPublicKey,
      signedTransaction
    } = ctx.request.body;

    if (!multisigAddress || !transactionIndex || !vote || !voterPublicKey || !signedTransaction) {
      ctx.status = 400;
      ctx.body = { error: '缺少必要参数' };
      return;
    }

    // 发送已签名的交易
    const signature = await connection.sendRawTransaction(
      Buffer.from(signedTransaction, 'base64')
    );

    // 等待交易确认
    const result = await connection.confirmTransaction(signature);

    ctx.body = {
      signature,
      result,
      success: true
    };
  } catch (error) {
    console.error('提交投票失败:', error);
    ctx.status = 500;
    ctx.body = { error: '提交投票失败: ' + error.message };
  }
});

// 构建执行指令
router.post('/api/multisig/build-execute-instruction', async (ctx) => {
  try {
    const {
      multisigAddress,
      transactionIndex,
      executorPublicKey
    } = ctx.request.body;

    if (!multisigAddress || !transactionIndex || !executorPublicKey) {
      ctx.status = 400;
      ctx.body = { error: '缺少必要参数' };
      return;
    }

    console.log(`构建执行指令 #${transactionIndex}...`);

    const multisigPda = new PublicKey(multisigAddress);
    const executorPubkey = new PublicKey(executorPublicKey);

    // 构建执行指令
    const instructionResult = await multisig.instructions.vaultTransactionExecute({
      connection,
      multisigPda,
      transactionIndex: BigInt(transactionIndex),
      member: executorPubkey,
      programId: MULTISIG_PROGRAM_ID,
    });

    console.log('执行指令构建成功');

    ctx.body = {
      instruction: {
        keys: instructionResult.keys.map(key => ({
          pubkey: key.pubkey.toBase58(),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: instructionResult.programId.toBase58(),
        data: Array.from(instructionResult.data)
      },
      lookupTableAccounts: []
    };
  } catch (error) {
    console.error('构建执行指令失败:', error);
    ctx.status = 500;
    ctx.body = { error: '构建执行指令失败: ' + error.message };
  }
});

// 执行提案
router.post('/api/multisig/execute', async (ctx) => {
  try {
    console.log('收到执行提案请求:', ctx.request.body);

    const {
      multisigAddress,
      transactionIndex,
      executorPublicKey,
      signedTransaction
    } = ctx.request.body;

    if (!multisigAddress || !transactionIndex || !executorPublicKey || !signedTransaction) {
      console.log('缺少必要参数:', { multisigAddress, transactionIndex, executorPublicKey, hasSignedTransaction: !!signedTransaction });
      ctx.status = 400;
      ctx.body = { error: '缺少必要参数' };
      return;
    }

    console.log(`执行提案 #${transactionIndex} 验证开始...`);

    // 验证提案是否可以执行
    const multisigPubkey = new PublicKey(multisigAddress);

    // 1. 检查提案是否存在
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (!proposalAccount) {
      console.log(`提案 #${transactionIndex} 不存在`);
      ctx.status = 400;
      ctx.body = { error: `提案 #${transactionIndex} 不存在` };
      return;
    }

    // 2. 检查交易是否存在
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (!transactionAccount) {
      console.log(`提案 #${transactionIndex} 没有对应的交易内容`);
      ctx.status = 400;
      ctx.body = {
        error: `提案 #${transactionIndex} 没有对应的交易内容，无法执行。这可能是一个旧的或不完整的提案。`
      };
      return;
    }

    // 3. 检查提案状态
    const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    const approvals = proposalData.approved.length;
    const threshold = multisigData.threshold;

    if (approvals < threshold) {
      console.log(`提案 #${transactionIndex} 投票不足: ${approvals}/${threshold}`);
      ctx.status = 400;
      ctx.body = { error: `提案 #${transactionIndex} 投票不足，需要 ${threshold} 票，当前只有 ${approvals} 票` };
      return;
    }

    if (proposalData.status.__kind === 'Executed') {
      console.log(`提案 #${transactionIndex} 已经执行过了`);
      ctx.status = 400;
      ctx.body = { error: `提案 #${transactionIndex} 已经执行过了` };
      return;
    }

    if (proposalData.status.__kind === 'Cancelled') {
      console.log(`提案 #${transactionIndex} 已被取消`);
      ctx.status = 400;
      ctx.body = { error: `提案 #${transactionIndex} 已被取消` };
      return;
    }

    // 4. 检查执行者是否是多签成员
    const executorPubkey = new PublicKey(executorPublicKey);
    const isMember = multisigData.members.some(member => member.key.equals(executorPubkey));
    if (!isMember) {
      console.log(`执行者不是多签成员`);
      ctx.status = 400;
      ctx.body = { error: '执行者不是多签成员' };
      return;
    }

    console.log(`提案 #${transactionIndex} 验证通过，开始发送交易...`);

    // 发送已签名的交易
    const signature = await connection.sendRawTransaction(
      Buffer.from(signedTransaction, 'base64')
    );

    console.log(`交易已发送，签名: ${signature}`);

    // 等待交易确认
    const result = await connection.confirmTransaction(signature);
    console.log(`交易确认结果:`, result);

    ctx.body = {
      signature,
      result,
      success: true
    };
  } catch (error) {
    console.error('执行提案失败:', error);
    ctx.status = 500;
    ctx.body = { error: '执行提案失败: ' + error.message };
  }
});

app.use(router.routes());
app.use(router.allowedMethods());

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Squads Backend Proxy Server running on http://localhost:${PORT}`);
  console.log(`📡 Connected to Solana Mainnet: ${SOLANA_RPC_URL}`);
  console.log(`🔗 Multisig Program ID: ${MULTISIG_PROGRAM_ID.toBase58()}`);
});

// 错误处理
app.on('error', (err, ctx) => {
  console.error('服务器错误:', err);
});