{"name": "squads-be-proxy", "version": "1.0.0", "description": "Squads Backend Proxy Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@solana/web3.js": "^1.87.6", "@sqds/multisig": "^2.1.0", "koa": "^2.15.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "koa-router": "^12.0.1", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["koa", "solana", "multisig", "proxy"], "author": "", "license": "MIT"}