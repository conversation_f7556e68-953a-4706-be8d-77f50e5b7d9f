import React, { useState } from 'react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { Send, FileText, Coins, Users, Home } from 'lucide-react';
import SolTransfer from './SolTransfer';
import TokenTransfer from './TokenTransfer';
import ProposalManagement from './ProposalManagement';
import MemberManagement from './MemberManagement';

type TabType = 'home' | 'sol-transfer' | 'token-transfer' | 'proposals' | 'members';

interface TabConfig {
  key: TabType;
  label: string;
  icon: React.ReactNode;
  component: React.ReactNode;
}

const Demo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('home');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleSuccess = (text: string) => {
    setMessage({ type: 'success', text });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleError = (text: string) => {
    setMessage({ type: 'error', text });
    setTimeout(() => setMessage(null), 5000);
  };

  const tabs: TabConfig[] = [
    {
      key: 'home',
      label: '首页',
      icon: <Home size={20} />,
      component: (
        <div style={{ textAlign: 'center', padding: '60px 20px' }}>
          <h1 style={{ color: '#333', marginBottom: '20px' }}>Squads 多签钱包</h1>
          <p style={{ fontSize: '18px', color: '#666', marginBottom: '40px' }}>
            基于 Solana 的多签钱包解决方案
          </p>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            maxWidth: '800px',
            margin: '0 auto'
          }}>
            <div style={{
              padding: '30px',
              background: '#f8f9fa',
              border: '1px solid #e9ecef',
              borderRadius: '12px',
              textAlign: 'center'
            }}>
              <Send size={32} color="#007bff" style={{ marginBottom: '16px' }} />
              <h3 style={{ margin: '0 0 12px 0' }}>SOL 转账</h3>
              <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                创建和管理 SOL 转账交易
              </p>
            </div>
            <div style={{
              padding: '30px',
              background: '#f8f9fa',
              border: '1px solid #e9ecef',
              borderRadius: '12px',
              textAlign: 'center'
            }}>
              <Coins size={32} color="#28a745" style={{ marginBottom: '16px' }} />
              <h3 style={{ margin: '0 0 12px 0' }}>Token 转账</h3>
              <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                管理 SPL Token 转账交易
              </p>
            </div>
            <div style={{
              padding: '30px',
              background: '#f8f9fa',
              border: '1px solid #e9ecef',
              borderRadius: '12px',
              textAlign: 'center'
            }}>
              <FileText size={32} color="#ffc107" style={{ marginBottom: '16px' }} />
              <h3 style={{ margin: '0 0 12px 0' }}>提案管理</h3>
              <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                查看和执行多签提案
              </p>
            </div>
            <div style={{
              padding: '30px',
              background: '#f8f9fa',
              border: '1px solid #e9ecef',
              borderRadius: '12px',
              textAlign: 'center'
            }}>
              <Users size={32} color="#6f42c1" style={{ marginBottom: '16px' }} />
              <h3 style={{ margin: '0 0 12px 0' }}>成员管理</h3>
              <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                查看多签账户成员信息
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'sol-transfer',
      label: 'SOL转账',
      icon: <Send size={20} />,
      component: <SolTransfer onSuccess={handleSuccess} onError={handleError} />
    },
    {
      key: 'token-transfer',
      label: 'Token转账',
      icon: <Coins size={20} />,
      component: <TokenTransfer onSuccess={handleSuccess} onError={handleError} />
    },
    {
      key: 'proposals',
      label: 'Transactions',
      icon: <FileText size={20} />,
      component: <ProposalManagement onSuccess={handleSuccess} onError={handleError} />
    },
    {
      key: 'members',
      label: 'Members',
      icon: <Users size={20} />,
      component: <MemberManagement onError={handleError} />
    }
  ];

  const activeTabConfig = tabs.find(tab => tab.key === activeTab);

  return (
    <div style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      {/* Header */}
      <header style={{
        background: '#fff',
        borderBottom: '1px solid #e0e0e0',
        padding: '0 20px',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '64px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            <h1 style={{ margin: 0, fontSize: '24px', color: '#333' }}>Squads NetWork</h1>
            <div style={{ fontSize: '12px', color: '#666', background: '#e3f2fd', padding: '4px 8px', borderRadius: '4px' }}>
              Mainnet
            </div>
          </div>
          <WalletMultiButton />
        </div>
      </header>

      {/* Navigation */}
      <nav style={{
        background: '#fff',
        borderBottom: '1px solid #e0e0e0',
        padding: '0 20px'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          gap: '0'
        }}>
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              style={{
                background: 'none',
                border: 'none',
                padding: '16px 24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: activeTab === tab.key ? '#007bff' : '#666',
                borderBottom: activeTab === tab.key ? '2px solid #007bff' : '2px solid transparent',
                fontSize: '14px',
                fontWeight: activeTab === tab.key ? '600' : '400',
                transition: 'all 0.2s ease'
              }}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>
      </nav>

      {/* Message Display */}
      {message && (
        <div style={{
          padding: '12px 20px',
          background: message.type === 'success' ? '#d4edda' : '#f8d7da',
          color: message.type === 'success' ? '#155724' : '#721c24',
          borderBottom: `1px solid ${message.type === 'success' ? '#c3e6cb' : '#f5c6cb'}`,
          textAlign: 'center'
        }}>
          {message.text}
        </div>
      )}

      {/* Main Content */}
      <main style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '32px 20px'
      }}>
        <div style={{
          background: '#fff',
          borderRadius: '12px',
          padding: '32px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          {activeTabConfig?.component}
        </div>
      </main>
    </div>
  );
};

export default Demo;