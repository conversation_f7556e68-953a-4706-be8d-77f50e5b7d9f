import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { Vote, CheckCircle, XCircle, Clock, Play, RefreshCw, AlertCircle } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import { apiService } from '../services/ApiService';

interface ProposalManagementProps {
  onSuccess?: (signature: string) => void;
  onError?: (error: string) => void;
}

interface Proposal {
  transactionIndex: number;
  status: 'Draft' | 'Active' | 'Approved' | 'Rejected' | 'Executed' | 'Cancelled';
  approvals: number;
  threshold: number;
  creator: string;
  memo?: string;
  votes: Array<{
    member: string;
    vote: 'Approve' | 'Reject';
  }>;
  canExecute: boolean;
}

const ProposalManagement: React.FC<ProposalManagementProps> = ({ onSuccess, onError }) => {
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string>('');
  const [status, setStatus] = useState<string>('');

  // 使用useRef存储回调函数，避免依赖变化
  const onErrorRef = useRef(onError);
  const onSuccessRef = useRef(onSuccess);

  // 更新ref
  useEffect(() => {
    onErrorRef.current = onError;
    onSuccessRef.current = onSuccess;
  }, [onError, onSuccess]);

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleCreateNew = () => {
    onErrorRef.current?.('创建新多签账户功能将在后续步骤中实现');
  };

  const loadProposals = useCallback(async () => {
    if (!selectedMultisig || !publicKey) return;

    setLoading(true);
    setStatus('正在加载提案...');

    try {
      const proposalData = await apiService.getProposals(selectedMultisig);
      setProposals(proposalData.proposals);
      setStatus('');
    } catch (error: any) {
      console.error('加载提案失败:', error);
      onErrorRef.current?.(error.message || '加载提案失败');
      setProposals([]);
    } finally {
      setLoading(false);
    }
  }, [selectedMultisig, publicKey]); // 移除onError依赖

  useEffect(() => {
    loadProposals();
  }, [loadProposals]);

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey) {
      onErrorRef.current?.('钱包不支持签名功能');
      return;
    }

    const actionKey = `${vote}-${transactionIndex}`;
    setActionLoading(actionKey);
    setStatus(`正在${vote === 'approve' ? '批准' : '拒绝'}提案 #${transactionIndex}...`);

    try {
      const multisigPda = new PublicKey(selectedMultisig);
      const { blockhash } = await apiService.getLatestBlockhash();

      let instruction;
      if (vote === 'approve') {
        instruction = multisig.instructions.proposalApprove({
          multisigPda,
          transactionIndex: BigInt(transactionIndex),
          member: publicKey,
          programId,
        });
      } else {
        instruction = multisig.instructions.proposalReject({
          multisigPda,
          transactionIndex: BigInt(transactionIndex),
          member: publicKey,
          programId,
        });
      }

      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTx = await signTransaction(transaction);

      const result = await apiService.submitVote({
        multisigAddress: selectedMultisig,
        transactionIndex: transactionIndex,
        vote: vote,
        voterPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
      });

      setStatus(`提案 #${transactionIndex} ${vote === 'approve' ? '批准' : '拒绝'}成功！`);
      onSuccessRef.current?.(result.signature);

      // 重新加载提案
      setTimeout(() => {
        loadProposals();
        setStatus('');
      }, 2000);

    } catch (error: any) {
      console.error('投票失败:', error);
      setStatus('');
      onErrorRef.current?.(error.message || '投票失败');
    } finally {
      setActionLoading('');
    }
  }, [publicKey, signTransaction, selectedMultisig, loadProposals]); // 移除onSuccess, onError依赖

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey) {
      onErrorRef.current?.('钱包不支持签名功能');
      return;
    }

    const actionKey = `execute-${transactionIndex}`;
    setActionLoading(actionKey);
    setStatus(`正在执行提案 #${transactionIndex}...`);

    try {
      console.log('开始执行提案:', transactionIndex);
      console.log('publicKey:', publicKey?.toBase58());
      console.log('selectedMultisig:', selectedMultisig);

      const multisigPda = new PublicKey(selectedMultisig);

      // 使用后端API获取区块哈希，确保使用正确的网络
      console.log('获取区块哈希...');
      const { blockhash } = await apiService.getLatestBlockhash();
      console.log('获取到区块哈希:', blockhash);

      setStatus(`构建执行指令...`);
      console.log('通过后端API构建执行指令...');

      // 使用后端API构建执行指令
      const instructionData = await apiService.buildExecuteInstruction({
        multisigAddress: selectedMultisig,
        transactionIndex: transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      });

      console.log('执行指令构建成功:', instructionData);

      // 重构指令数据
      const instruction = {
        keys: instructionData.instruction.keys.map(key => ({
          pubkey: new PublicKey(key.pubkey),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: new PublicKey(instructionData.instruction.programId),
        data: Buffer.from(instructionData.instruction.data),
      };

      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      console.log('创建TransactionMessage成功');

      const transaction = new VersionedTransaction(message.compileToV0Message());
      console.log('交易创建成功');

      setStatus(`请在钱包中签名...`);
      console.log('请求钱包签名...');
      const signedTx = await signTransaction(transaction);
      console.log('钱包签名成功');

      // 检查签名结果
      if (!signedTx) {
        throw new Error('钱包签名失败，返回值为空');
      }

      setStatus(`提交执行交易...`);
      console.log('提交执行交易到后端...');
      const result = await apiService.executeProposal({
        multisigAddress: selectedMultisig,
        transactionIndex: transactionIndex,
        executorPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
      });
      console.log('执行交易成功:', result);

      setStatus(`提案 #${transactionIndex} 执行成功！`);
      onSuccessRef.current?.(result.signature);

      // 重新加载提案
      setTimeout(() => {
        loadProposals();
        setStatus('');
      }, 2000);

    } catch (error: any) {
      console.error('执行失败:', error);
      setStatus('');

      // 提供更详细的错误信息
      let errorMessage = error.message || '执行失败';
      if (error.message?.includes('没有对应的交易内容')) {
        errorMessage = `${error.message}\n\n建议：请尝试创建新的转账交易，旧的提案可能无法执行。`;
      } else if (error.message?.includes('Access forbidden') || error.message?.includes('403')) {
        errorMessage = '网络访问受限，请检查网络连接或稍后重试。';
      }

      onErrorRef.current?.(errorMessage);
    } finally {
      setActionLoading('');
    }
  }, [publicKey, signTransaction, selectedMultisig, loadProposals]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return '#007bff';
      case 'Approved': return '#28a745';
      case 'Executed': return '#6c757d';
      case 'Rejected': return '#dc3545';
      case 'Cancelled': return '#6c757d';
      default: return '#ffc107';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active': return <Clock size={16} />;
      case 'Approved': return <CheckCircle size={16} />;
      case 'Executed': return <Play size={16} />;
      case 'Rejected': return <XCircle size={16} />;
      case 'Cancelled': return <XCircle size={16} />;
      default: return <AlertCircle size={16} />;
    }
  };

  const renderProposal = (proposal: Proposal) => {
    const userVote = proposal.votes.find(v => v.member === publicKey?.toBase58());
    const canVote = proposal.status === 'Active' && !userVote;
    const canExecute = proposal.canExecute && proposal.status === 'Approved';

    return (
      <div
        key={proposal.transactionIndex}
        style={{
          border: '1px solid #ddd',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '16px',
          background: 'white'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <h4 style={{ margin: 0, color: '#333' }}>提案 #{proposal.transactionIndex}</h4>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              color: getStatusColor(proposal.status),
              fontSize: '14px'
            }}>
              {getStatusIcon(proposal.status)}
              {proposal.status}
            </div>
          </div>
          <div style={{ fontSize: '14px', color: '#666' }}>
            {proposal.approvals}/{proposal.threshold} 票
          </div>
        </div>

        {proposal.memo && (
          <div style={{ marginBottom: '12px', fontSize: '14px', color: '#666' }}>
            <strong>备注:</strong> {proposal.memo}
          </div>
        )}

        <div style={{ marginBottom: '12px', fontSize: '14px', color: '#666' }}>
          <strong>创建者:</strong> {proposal.creator.slice(0, 8)}...{proposal.creator.slice(-4)}
        </div>

        {proposal.votes.length > 0 && (
          <div style={{ marginBottom: '12px' }}>
            <strong style={{ fontSize: '14px', color: '#333' }}>投票情况:</strong>
            <div style={{ marginTop: '4px' }}>
              {proposal.votes.map((vote, index) => (
                <div key={index} style={{ fontSize: '12px', color: '#666', marginBottom: '2px' }}>
                  {vote.member.slice(0, 8)}...{vote.member.slice(-4)}:
                  <span style={{
                    color: vote.vote === 'Approve' ? '#28a745' : '#dc3545',
                    marginLeft: '4px'
                  }}>
                    {vote.vote === 'Approve' ? '✓ 批准' : '✗ 拒绝'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {userVote && (
          <div style={{
            padding: '8px 12px',
            background: userVote.vote === 'Approve' ? '#d4edda' : '#f8d7da',
            borderRadius: '4px',
            marginBottom: '12px',
            fontSize: '14px'
          }}>
            您已投票: {userVote.vote === 'Approve' ? '✓ 批准' : '✗ 拒绝'}
          </div>
        )}

        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          {canVote && (
            <>
              <button
                onClick={() => handleVote(proposal.transactionIndex, 'approve')}
                disabled={actionLoading === `approve-${proposal.transactionIndex}`}
                style={{
                  padding: '8px 16px',
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: actionLoading === `approve-${proposal.transactionIndex}` ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                {actionLoading === `approve-${proposal.transactionIndex}` ? (
                  <RefreshCw size={14} className="animate-spin" />
                ) : (
                  <CheckCircle size={14} />
                )}
                批准
              </button>
              <button
                onClick={() => handleVote(proposal.transactionIndex, 'reject')}
                disabled={actionLoading === `reject-${proposal.transactionIndex}`}
                style={{
                  padding: '8px 16px',
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: actionLoading === `reject-${proposal.transactionIndex}` ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                {actionLoading === `reject-${proposal.transactionIndex}` ? (
                  <RefreshCw size={14} className="animate-spin" />
                ) : (
                  <XCircle size={14} />
                )}
                拒绝
              </button>
            </>
          )}

          {canExecute && (
            <button
              onClick={() => handleExecute(proposal.transactionIndex)}
              disabled={actionLoading === `execute-${proposal.transactionIndex}`}
              style={{
                padding: '8px 16px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: actionLoading === `execute-${proposal.transactionIndex}` ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              {actionLoading === `execute-${proposal.transactionIndex}` ? (
                <RefreshCw size={14} className="animate-spin" />
              ) : (
                <Play size={14} />
              )}
              执行
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <MultisigSelector
        selectedMultisig={selectedMultisig}
        onSelect={setSelectedMultisig}
        onCreateNew={handleCreateNew}
      />

      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3 style={{ margin: 0, color: '#333' }}>提案管理</h3>
        <button
          onClick={loadProposals}
          disabled={loading}
          style={{
            padding: '8px 16px',
            background: loading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
        >
          <RefreshCw size={14} className={loading ? 'animate-spin' : ''} />
          刷新
        </button>
      </div>

      {status && (
        <div style={{
          padding: '12px',
          background: '#f0f8ff',
          borderRadius: '6px',
          color: '#007bff',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <AlertCircle size={16} />
          {status}
        </div>
      )}

      {!selectedMultisig ? (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: '#666',
          background: '#f8f9fa',
          borderRadius: '6px'
        }}>
          请先选择多签账户
        </div>
      ) : proposals.length === 0 && !loading ? (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: '#666',
          background: '#f8f9fa',
          borderRadius: '6px'
        }}>
          <Vote size={48} style={{ margin: '0 auto 16px', display: 'block' }} />
          暂无提案
          <p style={{ fontSize: '14px', marginTop: '8px' }}>
            创建转账交易后，提案将在这里显示
          </p>
        </div>
      ) : (
        <div>
          {proposals.map(renderProposal)}
        </div>
      )}

      <div style={{ fontSize: '14px', color: '#666', marginTop: '20px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
          <Vote size={16} />
          <strong>提案管理说明:</strong>
        </div>
        <ul style={{ paddingLeft: '20px', lineHeight: '1.5' }}>
          <li>查看所有待处理的多签交易提案</li>
          <li>对提案进行投票（批准或拒绝）</li>
          <li>当达到多签阈值时，可以执行已批准的提案</li>
          <li>只有多签成员可以投票和执行提案</li>
          <li>每个成员对每个提案只能投票一次</li>
        </ul>
      </div>
    </div>
  );
};

export default ProposalManagement;