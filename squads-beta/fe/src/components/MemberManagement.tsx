import React, { useState, useEffect, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { Users, Shield, Clock, AlertCircle, CheckCircle, User } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import { apiService } from '../services/ApiService';

interface MemberInfo {
  key: string;
  permissions: any;
}

interface MultisigInfo {
  address: string;
  members: MemberInfo[];
  threshold: number;
  transactionIndex: number;
  createKey: string;
  allowExternalExecute: boolean;
  rentCollector?: string;
}

interface MemberManagementProps {
  onError?: (error: string) => void;
}

const MemberManagement: React.FC<MemberManagementProps> = ({ onError }) => {
  const { publicKey } = useWallet();
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [multisigInfo, setMultisigInfo] = useState<MultisigInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string>('');

  const handleCreateNew = () => {
    onError?.('创建新多签账户功能将在后续步骤中实现');
  };

  const loadMultisigInfo = useCallback(async () => {
    if (!selectedMultisig || !publicKey) {
      setMultisigInfo(null);
      return;
    }

    try {
      setLoading(true);
      setStatus('获取多签信息...');

      const info = await apiService.getMultisigDetails(selectedMultisig);
      setMultisigInfo(info);
      setStatus('');
    } catch (error: any) {
      console.error('获取多签信息失败:', error);
      onError?.(error.message || '获取多签信息失败');
      setMultisigInfo(null);
    } finally {
      setLoading(false);
    }
  }, [selectedMultisig, publicKey, onError]);

  useEffect(() => {
    loadMultisigInfo();
  }, [loadMultisigInfo]);

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  const getCurrentUserRole = () => {
    if (!multisigInfo || !publicKey) return null;

    const userMember = multisigInfo.members.find(member =>
      member.key === publicKey.toBase58()
    );

    if (!userMember) return null;

    // 检查是否是创建者
    if (multisigInfo.createKey === publicKey.toBase58()) {
      return 'Creator';
    }

    return 'Member';
  };

  return (
    <div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '24px' }}>
        <Users size={24} color="#007bff" />
        <h2 style={{ margin: 0, color: '#333' }}>成员管理</h2>
      </div>

      <MultisigSelector
        selectedMultisig={selectedMultisig}
        onSelect={setSelectedMultisig}
        onCreateNew={handleCreateNew}
      />

      {status && (
        <div style={{
          padding: '12px',
          background: '#f0f8ff',
          borderRadius: '6px',
          color: '#007bff',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <AlertCircle size={16} />
          {status}
        </div>
      )}

      {multisigInfo && (
        <div style={{ marginTop: '24px' }}>
          {/* 多签基本信息 */}
          <div style={{
            background: '#f8f9fa',
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '20px',
            marginBottom: '24px'
          }}>
            <h3 style={{ margin: '0 0 16px 0', color: '#333', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Shield size={20} />
              多签账户信息
            </h3>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
              <div>
                <label style={{ display: 'block', fontWeight: 'bold', color: '#666', marginBottom: '4px' }}>
                  地址:
                </label>
                <div style={{ fontFamily: 'monospace', fontSize: '14px', wordBreak: 'break-all' }}>
                  {multisigInfo.address}
                </div>
              </div>

              <div>
                <label style={{ display: 'block', fontWeight: 'bold', color: '#666', marginBottom: '4px' }}>
                  阈值配置:
                </label>
                <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#007bff' }}>
                  {multisigInfo.threshold} / {multisigInfo.members.length}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  需要 {multisigInfo.threshold} 个成员签名才能执行交易
                </div>
              </div>

              <div>
                <label style={{ display: 'block', fontWeight: 'bold', color: '#666', marginBottom: '4px' }}>
                  交易索引:
                </label>
                <div style={{ fontSize: '16px' }}>
                  #{multisigInfo.transactionIndex}
                </div>
              </div>

            </div>

            {multisigInfo.rentCollector && (
              <div style={{ marginTop: '16px' }}>
                <label style={{ display: 'block', fontWeight: 'bold', color: '#666', marginBottom: '4px' }}>
                  租金收集者:
                </label>
                <div style={{ fontFamily: 'monospace', fontSize: '14px', wordBreak: 'break-all' }}>
                  {multisigInfo.rentCollector}
                </div>
              </div>
            )}
          </div>

          {/* 成员列表 */}
          <div style={{
            background: '#fff',
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            overflow: 'hidden'
          }}>
            <div style={{
              background: '#f8f9fa',
              padding: '16px',
              borderBottom: '1px solid #e9ecef'
            }}>
              <h3 style={{ margin: 0, color: '#333', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <User size={20} />
                成员列表 ({multisigInfo.members.length})
              </h3>
            </div>

            <div style={{ padding: '0' }}>
              {multisigInfo.members.map((member, index) => {
                const isCreator = member.key === multisigInfo.createKey;
                const isCurrent = isCurrentUser(member.key);

                return (
                  <div
                    key={member.key}
                    style={{
                      padding: '16px',
                      borderBottom: index < multisigInfo.members.length - 1 ? '1px solid #f0f0f0' : 'none',
                      background: isCurrent ? '#f0f8ff' : '#fff'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <div style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          background: isCurrent ? '#007bff' : '#6c757d',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: '#fff',
                          fontSize: '14px',
                          fontWeight: 'bold'
                        }}>
                          {index + 1}
                        </div>

                        <div>
                          <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
                            成员 #{index + 1}
                            {isCurrent && (
                              <span style={{
                                color: '#007bff',
                                fontSize: '12px',
                                marginLeft: '8px',
                                background: '#e3f2fd',
                                padding: '2px 6px',
                                borderRadius: '4px'
                              }}>
                                (当前用户)
                              </span>
                            )}
                            {isCreator && (
                              <span style={{
                                color: '#28a745',
                                fontSize: '12px',
                                marginLeft: '8px',
                                background: '#e8f5e8',
                                padding: '2px 6px',
                                borderRadius: '4px'
                              }}>
                                创建者
                              </span>
                            )}
                          </div>
                          <div style={{
                            fontFamily: 'monospace',
                            fontSize: '14px',
                            color: '#666',
                            marginTop: '4px'
                          }}>
                            {formatAddress(member.key, 12)}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div style={{
                      fontSize: '12px',
                      color: '#666',
                      background: '#f8f9fa',
                      padding: '8px',
                      borderRadius: '4px',
                      fontFamily: 'monospace',
                      wordBreak: 'break-all'
                    }}>
                      完整地址: {member.key}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 当前用户信息 */}
          {getCurrentUserRole() && (
            <div style={{
              marginTop: '16px',
              padding: '16px',
              background: '#e3f2fd',
              border: '1px solid #bbdefb',
              borderRadius: '8px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                <User size={16} color="#1976d2" />
                <strong style={{ color: '#1976d2' }}>您的角色信息</strong>
              </div>
              <div style={{ fontSize: '14px', color: '#333' }}>
                <div>角色: <strong>{getCurrentUserRole()}</strong></div>
                <div>地址: <code>{publicKey?.toBase58()}</code></div>
                <div>状态: <span style={{ color: '#28a745' }}>已连接</span></div>
              </div>
            </div>
          )}

          {/* 功能说明 */}
          <div style={{
            marginTop: '24px',
            padding: '16px',
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '8px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
              <AlertCircle size={16} color="#856404" />
              <strong style={{ color: '#856404' }}>功能说明</strong>
            </div>
            <ul style={{ margin: '0', paddingLeft: '20px', color: '#856404', fontSize: '14px' }}>
              <li>当前仅支持查看多签账户的成员信息</li>
              <li>阈值配置决定了需要多少个成员签名才能执行交易</li>
              <li>创建者拥有特殊权限，可以进行某些管理操作</li>
              <li>成员新增和删除功能将在后续版本中实现</li>
            </ul>
          </div>
        </div>
      )}

      {!multisigInfo && selectedMultisig && !loading && (
        <div style={{
          padding: '40px',
          textAlign: 'center',
          color: '#666',
          background: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '8px',
          marginTop: '24px'
        }}>
          <Users size={48} color="#ccc" style={{ marginBottom: '16px' }} />
          <div>请选择一个多签账户来查看成员信息</div>
        </div>
      )}
    </div>
  );
};

export default MemberManagement;