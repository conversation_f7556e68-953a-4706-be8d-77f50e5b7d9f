const API_BASE_URL = 'http://localhost:3001';

export interface MultisigInfo {
  address: string;
  members: Array<{
    key: string;
    permissions: any;
  }>;
  threshold: number;
  transactionIndex: number;
}

export interface VaultInfo {
  address: string;
  balance: number;
  balanceSOL: number;
}

export interface VaultInfoResponse {
  multisigInfo: MultisigInfo;
  vaultInfo: VaultInfo;
}

export interface CreateSolTransferRequest {
  multisigAddress: string;
  recipientAddress: string;
  amount: string;
  creatorPublicKey: string;
  signedTransaction: string;
}

export interface CreateSolTransferResponse {
  signature: string;
  result: any;
  success: boolean;
}

export interface CreateTokenTransferRequest {
  multisigAddress: string;
  recipientAddress: string;
  tokenMint: string;
  amount: string;
  decimals: number;
  creatorPublicKey: string;
  signedTransaction: string;
}

export interface CreateTokenTransferResponse {
  signature: string;
  result: any;
  success: boolean;
}

export interface TokenBalanceResponse {
  balance: number;
  decimals: number;
  uiAmount: number;
}

export interface Proposal {
  transactionIndex: number;
  status: 'Draft' | 'Active' | 'Approved' | 'Rejected' | 'Executed' | 'Cancelled';
  approvals: number;
  threshold: number;
  creator: string;
  memo?: string;
  votes: Array<{
    member: string;
    vote: 'Approve' | 'Reject';
  }>;
  canExecute: boolean;
}

export interface ProposalsResponse {
  proposals: Proposal[];
}

export interface SubmitVoteRequest {
  multisigAddress: string;
  transactionIndex: number;
  vote: 'approve' | 'reject';
  voterPublicKey: string;
  signedTransaction: string;
}

export interface SubmitVoteResponse {
  signature: string;
  result: any;
  success: boolean;
}

export interface ExecuteProposalRequest {
  multisigAddress: string;
  transactionIndex: number;
  executorPublicKey: string;
  signedTransaction: string;
}

export interface ExecuteProposalResponse {
  signature: string;
  result: any;
  success: boolean;
}

export interface BuildExecuteInstructionRequest {
  multisigAddress: string;
  transactionIndex: number;
  executorPublicKey: string;
}

export interface BuildExecuteInstructionResponse {
  instruction: {
    keys: Array<{
      pubkey: string;
      isSigner: boolean;
      isWritable: boolean;
    }>;
    programId: string;
    data: number[];
  };
  lookupTableAccounts: any[];
}

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  async getMultisigAccounts(publicKey: string) {
    return this.request<{ multisigs: any[] }>('/api/multisig/accounts', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
  }

  async getMultisigDetails(multisigAddress: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/multisig/details`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        multisigAddress
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`获取多签详情失败: ${errorText}`);
    }

    return await response.json();
  }

  async getVaultInfo(multisigAddress: string): Promise<VaultInfoResponse> {
    return this.request<VaultInfoResponse>('/api/multisig/get-vault-info', {
      method: 'POST',
      body: JSON.stringify({ multisigAddress }),
    });
  }

  async createSolTransfer(request: CreateSolTransferRequest): Promise<CreateSolTransferResponse> {
    return this.request<CreateSolTransferResponse>('/api/multisig/create-sol-transfer', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async createTokenTransfer(request: CreateTokenTransferRequest): Promise<CreateTokenTransferResponse> {
    return this.request<CreateTokenTransferResponse>('/api/multisig/create-token-transfer', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getProposals(multisigAddress: string): Promise<ProposalsResponse> {
    return this.request<ProposalsResponse>('/api/multisig/proposals', {
      method: 'POST',
      body: JSON.stringify({ multisigAddress }),
    });
  }

  async submitVote(request: SubmitVoteRequest): Promise<SubmitVoteResponse> {
    return this.request<SubmitVoteResponse>('/api/multisig/vote', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async buildExecuteInstruction(request: BuildExecuteInstructionRequest): Promise<BuildExecuteInstructionResponse> {
    return this.request<BuildExecuteInstructionResponse>('/api/multisig/build-execute-instruction', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async executeProposal(request: ExecuteProposalRequest): Promise<ExecuteProposalResponse> {
    return this.request<ExecuteProposalResponse>('/api/multisig/execute', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getBalance(publicKey: string) {
    return this.request<{ balance: number }>('/api/solana/balance', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
  }

  async getTokenBalance(tokenAccount: string): Promise<TokenBalanceResponse> {
    return this.request<TokenBalanceResponse>('/api/solana/token-balance', {
      method: 'POST',
      body: JSON.stringify({ tokenAccount }),
    });
  }

  async getLatestBlockhash() {
    return this.request<{ blockhash: string }>('/api/solana/latest-blockhash');
  }
}

export const apiService = new ApiService();