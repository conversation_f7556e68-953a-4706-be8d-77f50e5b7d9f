const API_BASE_URL = 'http://localhost:3001';

export class ApiService {
  private static baseUrl = API_BASE_URL;

  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // 获取多签账户
  static async getMultisigAccounts(publicKey: string) {
    return this.request<{ multisigs: any[] }>('/api/multisig/accounts', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
  }

  // 获取账户余额
  static async getBalance(publicKey: string) {
    return this.request<{ balance: number }>('/api/solana/balance', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
  }

  // 获取最新区块哈希
  static async getLatestBlockhash() {
    return this.request<{ blockhash: string }>('/api/solana/latest-blockhash');
  }

  // 发送交易
  static async sendTransaction(transaction: string) {
    return this.request<{ signature: string }>('/api/solana/send-transaction', {
      method: 'POST',
      body: JSON.stringify({ transaction }),
    });
  }

  // 确认交易
  static async confirmTransaction(signature: string) {
    return this.request<{ result: any }>('/api/solana/confirm-transaction', {
      method: 'POST',
      body: JSON.stringify({ signature }),
    });
  }

  // 通用RPC调用
  static async rpcCall(method: string, params: any[] = []) {
    return this.request<any>('/api/solana/rpc', {
      method: 'POST',
      body: JSON.stringify({ method, params }),
    });
  }

  // 健康检查
  static async healthCheck() {
    return this.request<{ status: string; timestamp: string }>('/health');
  }
}

export default ApiService;