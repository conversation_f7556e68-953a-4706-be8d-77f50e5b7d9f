/* 导入钱包适配器的默认样式 */
@import '@solana/wallet-adapter-react-ui/styles.css';

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e0e0e0;
}

.step-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.button {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.button:hover {
  background: #0056b3;
}

.button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 钱包按钮样式覆盖 */
.wallet-adapter-button {
  background: #007bff !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 12px 24px !important;
}

.wallet-adapter-button:hover {
  background: #0056b3 !important;
}

/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  background: #f5f5f5;
}

h1 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #007bff;
}

input, select, textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 16px;
  box-sizing: border-box;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group {
  margin-bottom: 16px;
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 4px;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  margin-top: 4px;
}

.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .card {
    padding: 16px;
  }

  h1 {
    font-size: 24px;
  }
}