import { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { apiService } from '../services/ApiService';

interface MultisigAccount {
  address: string;
  members: number;
  threshold: number;
  transactionIndex: number;
}

export const useMultisigAccounts = () => {
  const { publicKey } = useWallet();
  const [multisigs, setMultisigs] = useState<MultisigAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!publicKey) {
      setMultisigs([]);
      setError(null);
      return;
    }

    const fetchMultisigs = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await apiService.getMultisigAccounts(publicKey.toBase58());
        setMultisigs(result.multisigs);
      } catch (err: any) {
        console.error('获取多签账户失败:', err);
        setError(err.message || '获取多签账户失败');
        setMultisigs([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMultisigs();
  }, [publicKey]);

  return { multisigs, loading, error };
};