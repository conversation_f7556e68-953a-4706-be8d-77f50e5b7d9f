<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SQUADS</title>
    <script>
      // Global polyfills
      if (typeof global === 'undefined') {
        var global = globalThis;
      }
      if (typeof process === 'undefined') {
        var process = { env: {} };
      }
      // Buffer polyfill
      if (typeof Buffer === 'undefined') {
        window.Buffer = undefined; // Will be set by the buffer module
      }
    </script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: #f5f5f5;
        color: #333;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .card {
        background: white;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .button {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
      }

      .button:hover {
        background: #0056b3;
      }

      .button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .step-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 16px;
        color: #007bff;
      }

      /* Tailwind-like utility classes */
      .flex {
        display: flex;
      }

      .items-center {
        align-items: center;
      }

      .justify-between {
        justify-content: space-between;
      }

      .justify-center {
        justify-content: center;
      }

      .gap-2 {
        gap: 8px;
      }

      .p-3 {
        padding: 12px;
      }

      .p-6 {
        padding: 24px;
      }

      .mb-2 {
        margin-bottom: 8px;
      }

      .mb-4 {
        margin-bottom: 16px;
      }

      .mt-1 {
        margin-top: 4px;
      }

      .text-sm {
        font-size: 14px;
      }

      .text-xs {
        font-size: 12px;
      }

      .text-gray-500 {
        color: #6b7280;
      }

      .text-gray-400 {
        color: #9ca3af;
      }

      .text-gray-700 {
        color: #374151;
      }

      .text-red-500 {
        color: #ef4444;
      }

      .text-blue-500 {
        color: #3b82f6;
      }

      .text-blue-700 {
        color: #1d4ed8;
      }

      .font-medium {
        font-weight: 500;
      }

      .rounded-lg {
        border-radius: 8px;
      }

      .border {
        border: 1px solid #e5e7eb;
      }

      .border-2 {
        border-width: 2px;
      }

      .border-dashed {
        border-style: dashed;
      }

      .border-gray-200 {
        border-color: #e5e7eb;
      }

      .border-gray-300 {
        border-color: #d1d5db;
      }

      .border-blue-500 {
        border-color: #3b82f6;
      }

      .bg-blue-50 {
        background-color: #eff6ff;
      }

      .bg-blue-500 {
        background-color: #3b82f6;
      }

      .bg-blue-600 {
        background-color: #2563eb;
      }

      .hover\:border-gray-300:hover {
        border-color: #d1d5db;
      }

      .hover\:border-gray-400:hover {
        border-color: #9ca3af;
      }

      .hover\:text-gray-600:hover {
        color: #4b5563;
      }

      .hover\:text-blue-700:hover {
        color: #1d4ed8;
      }

      .hover\:bg-blue-600:hover {
        background-color: #2563eb;
      }

      .transition-colors {
        transition: color 0.2s, background-color 0.2s, border-color 0.2s;
      }

      .cursor-pointer {
        cursor: pointer;
      }

      .w-full {
        width: 100%;
      }

      .mx-auto {
        margin-left: auto;
        margin-right: auto;
      }

      .text-center {
        text-align: center;
      }

      .space-y-2 > * + * {
        margin-top: 8px;
      }

      .inline-flex {
        display: inline-flex;
      }

      .animate-spin {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      .wallet-adapter-modal-overlay {
        background: rgba(0, 0, 0, 0.5);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      }

      .wallet-adapter-modal {
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 400px;
        width: 90%;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>