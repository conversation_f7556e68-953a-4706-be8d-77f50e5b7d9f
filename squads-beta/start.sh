#!/bin/bash

echo "🚀 启动 Squads Demo 服务..."

# 检查是否在正确的目录
if [ ! -d "fe" ] || [ ! -d "be" ]; then
    echo "❌ 错误: 请在 squads-beta 目录下运行此脚本"
    exit 1
fi

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    pkill -f "node server.js" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    echo "✅ 服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 启动后端服务
echo "📡 启动后端服务..."
cd be
if [ ! -d "node_modules" ]; then
    echo "📦 安装后端依赖..."
    npm install
fi

# 停止可能已经运行的服务
pkill -f "node server.js" 2>/dev/null || true
sleep 1

# 启动后端
nohup node server.js > server.log 2>&1 &
BE_PID=$!
echo "✅ 后端服务已启动 (PID: $BE_PID) - http://localhost:3001"

# 等待后端启动
sleep 3

# 检查后端是否正常启动
if curl -s http://localhost:3001/health > /dev/null; then
    echo "✅ 后端服务健康检查通过"
else
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 启动前端服务
echo "🌐 启动前端服务..."
cd ../fe
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 停止可能已经运行的前端服务
pkill -f "vite" 2>/dev/null || true
sleep 1

# 启动前端
npm run dev &
FE_PID=$!
echo "✅ 前端服务已启动 (PID: $FE_PID) - http://localhost:5173"

# 等待前端启动
sleep 5

# 检查前端是否正常启动
if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ 前端服务健康检查通过"
else
    echo "❌ 前端服务启动失败"
fi

echo ""
echo "🎉 所有服务已启动完成!"
echo "📱 前端: http://localhost:5173"
echo "🔧 后端: http://localhost:3001"
echo "❤️  健康检查: http://localhost:3001/health"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait