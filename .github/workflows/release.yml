name: Release

on:
  push:
    branches: [master]

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    env:
      # Enables Turborepo remote caching.
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v3

      - name: Install pnpm
        id: install-pnpm
        uses: pnpm/action-setup@v4
        with:
            version: 10.12.1
            run_install: false

      - name: Install Node.js
        id: install-node
        uses: actions/setup-node@v4
        with:
            node-version: 20
            cache: 'pnpm'

      - name: Get pnpm store directory
        id: pnpm-store
        shell: bash
        run: |
            echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        id: pnpm-cache
        uses: actions/cache@v3
        with:
            path: ${{ steps.pnpm-store.outputs.STORE_PATH }}
            key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
            restore-keys: |
                ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        id: install-deps
        run: pnpm install --frozen-lockfile

      - name: Create release pull request OR build, test, and publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm run release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
