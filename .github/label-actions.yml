question:
  issues:
    # Post a comment, `{issue-author}` is an optional placeholder
    comment: >
      Hi @{issue-author},


      Thanks for your question!


      We want to make sure to keep signal strong in the GitHub issue tracker &ndash; to make sure that it remains the
      best place to track issues that affect the development of Wallet Adapter itself.


      Questions like yours deserve a purpose-built Q & A forum. Unless there exists evidence that this is a bug with
      Wallet Adapter itself, please post your question to the Solana Stack Exchange using this link:


      https://solana.stackexchange.com/questions/ask


      ---


      _This
      [automated message](https://github.com/solana-labs/solana/blob/master/.github/label-actions.yml)
      is a result of having added the &lsquo;question&rsquo; tag_.

    # Close the issue
    close: true

use-wallet-standard:
  issues:
    # Post a comment, `{issue-author}` is an optional placeholder
    comment: >
      Hi @{issue-author},


      Thanks for your contribution. We are no longer accepting PRs of adapters for this type of wallet. You can
      [read about this policy here](https://github.com/anza-xyz/wallet-adapter/blob/master/WALLET.md),
      but more importantly, please check out
      [this guide for wallets](https://github.com/anza-xyz/wallet-standard/blob/master/WALLET.md)
      to implement the Wallet Standard.


      However, most of what you've written for this adapter is portable to the Wallet Standard interface, and
      implementing this interface will grant your wallet distribution on dapps across Solana. After reading the guide
      and looking at the reference implementations, if you have questions on how to do this, please open an issue here:


      https://github.com/anza-xyz/wallet-standard


      ---


      _This
      [automated message](https://github.com/anza-xyz/wallet-adapter/blob/master/.github/label-actions.yml)
      is a result of having added the &lsquo;use-wallet-standard&rsquo; tag_.

    # Close the issue
    close: true
