# @solana/wallet-adapter-react-ui

## 0.9.39

### Patch Changes

- 75bf350: Update dependencies
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27
    - @solana/wallet-adapter-react@0.15.39
    - @solana/wallet-adapter-base-ui@0.1.6

## 0.9.38

### Patch Changes

- db923f1: Use Node 20+ rather than 22
- Updated dependencies [db923f1]
    - @solana/wallet-adapter-react@0.15.38
    - @solana/wallet-adapter-base-ui@0.1.5
    - @solana/wallet-adapter-base@0.9.26

## 0.9.37

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-react@0.15.37
    - @solana/wallet-adapter-base-ui@0.1.4
    - @solana/wallet-adapter-base@0.9.25

## 0.9.36

### Patch Changes

- c96cae47: The base version of Node has been raised to v20
- Updated dependencies [e25e7971]
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-react@0.15.36
    - @solana/wallet-adapter-base-ui@0.1.3
    - @solana/wallet-adapter-base@0.9.24

## 0.9.35

### Patch Changes

- cb29215: Simplify wallet ordering to respect order provided by application

## 0.9.34

### Patch Changes

- Updated dependencies [bdc0eff]
    - @solana/wallet-adapter-react@0.15.35
    - @solana/wallet-adapter-base-ui@0.1.2

## 0.9.33

### Patch Changes

- Updated dependencies [a3d35a1]
    - @solana/wallet-adapter-react@0.15.34
    - @solana/wallet-adapter-base@0.9.23
    - @solana/wallet-adapter-base-ui@0.1.1

## 0.9.32

### Patch Changes

- 7b06737: Use wallet button hooks from base-ui package
- ba57f75: feat: extract wallet buttons and text labels into separate components
  Now that the wallet connection state is an enum, it makes it easier to extract the labels from the components. You can now bring your own i18n framework to bear on the `Base*` version of `WalletConnectButton`, `WalletDisconnectButton`, and `WalletMultiButton` to inject your own translated labels.
- Updated dependencies [7b06737]
- Updated dependencies [ba57f75]
- Updated dependencies [7c6f2e1]
- Updated dependencies [7b06737]
    - @solana/wallet-adapter-react@0.15.33
    - @solana/wallet-adapter-base-ui@0.1.0

## 0.9.31

### Patch Changes

- Updated dependencies [f62ce364]
    - @solana/wallet-adapter-react@0.15.32

## 0.9.30

### Patch Changes

- Updated dependencies [61d62efa]
    - @solana/wallet-adapter-react@0.15.31

## 0.9.29

### Patch Changes

- 8a8fdc72: Update dependencies
- Updated dependencies [8a8fdc72]
    - @solana/wallet-adapter-react@0.15.30
    - @solana/wallet-adapter-base@0.9.22

## 0.9.28

### Patch Changes

- Updated dependencies [f99c2154]
    - @solana/wallet-adapter-base@0.9.21
    - @solana/wallet-adapter-react@0.15.29

## 0.9.27

### Patch Changes

- Updated dependencies [0a5f56e]
    - @solana/wallet-adapter-react@0.15.28

## 0.9.26

### Patch Changes

- Updated dependencies [faf61e6]
    - @solana/wallet-adapter-react@0.15.27

## 0.9.25

### Patch Changes

- e2a5b34: UI tweaks for wallet modal/dialogs

## 0.9.24

### Patch Changes

- 912cc0e: Allow wallets to customize autoConnect handling, adding support for Phantom deep links on iOS
- Updated dependencies [912cc0e]
    - @solana/wallet-adapter-base@0.9.20
    - @solana/wallet-adapter-react@0.15.26

## 0.9.23

### Patch Changes

- 65bb9fb: Mark stylesheet as having side effects (#652)
- Updated dependencies [353f2a5]
    - @solana/wallet-adapter-base@0.9.19
    - @solana/wallet-adapter-react@0.15.25

## 0.9.22

### Patch Changes

- Updated dependencies [21200bc]
    - @solana/wallet-adapter-react@0.15.24

## 0.9.21

### Patch Changes

- Updated dependencies [0e62d22]
    - @solana/wallet-adapter-react@0.15.23

## 0.9.20

### Patch Changes

- 5d016a2: Mobile Wallet Adapter and Wallet Standard support in `@solana/wallet-adapter-react`

    - Early Access + Upgrade Guide: https://github.com/solana-labs/wallet-adapter/issues/604
    - Changes in this release: https://github.com/solana-labs/wallet-adapter/pull/598

- Updated dependencies [5d016a2]
    - @solana/wallet-adapter-react@0.15.22
