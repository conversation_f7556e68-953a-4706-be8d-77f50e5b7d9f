{"name": "@solana/wallet-adapter-react-ui", "version": "0.9.39", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["lib", "src", "LICENSE", "styles.css"], "engines": {"node": ">=20"}, "type": "module", "sideEffects": ["styles.css"], "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {".": {"import": "./lib/esm/index.js", "require": "./lib/cjs/index.js", "types": "./lib/types/index.d.ts"}, "./styles.css": "./styles.css"}, "scripts": {"build": "tsc --build --verbose && pnpm run package", "clean": "shx mkdir -p lib && shx rm -rf lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "package": "shx mkdir -p lib/cjs && shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json"}, "peerDependencies": {"@solana/web3.js": "^1.98.0", "react": "*", "react-dom": "*"}, "dependencies": {"@solana/wallet-adapter-base": "workspace:^", "@solana/wallet-adapter-base-ui": "workspace:^", "@solana/wallet-adapter-react": "workspace:^"}, "devDependencies": {"@solana/web3.js": "^1.98.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "shx": "^0.4.0"}}