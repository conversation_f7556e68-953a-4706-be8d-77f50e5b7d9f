{"name": "@solana/wallet-adapter-react-ui-starter", "version": "0.9.38", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["src", ".editorconfig", ".env", ".giti<PERSON>re", ".prettieri<PERSON>re", ".prettier<PERSON>", "LICENSE", "package.json", "tsconfig.json"], "scripts": {"build": "tsc --build --verbose && parcel build src/index.html", "clean": "shx mkdir -p .parcel-cache dist lib && shx rm -rf .parcel-cache dist lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "start": "parcel src/index.html"}, "dependencies": {"@solana/wallet-adapter-base": "workspace:^", "@solana/wallet-adapter-react": "workspace:^", "@solana/wallet-adapter-react-ui": "workspace:^", "@solana/wallet-adapter-wallets": "workspace:^", "@solana/web3.js": "^1.98.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "parcel": "^2.15.2", "prettier": "^3.5.3", "process": "^0.11.10", "shx": "^0.4.0", "typescript": "^5.8.3"}}