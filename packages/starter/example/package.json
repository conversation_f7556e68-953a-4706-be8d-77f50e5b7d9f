{"name": "@solana/wallet-adapter-example", "version": "0.18.41", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["src", ".editorconfig", ".env", ".giti<PERSON>re", ".prettieri<PERSON>re", ".prettier<PERSON>", "LICENSE", "package.json", "tsconfig.json"], "scripts": {"build": "tsc --build --verbose && parcel build src/index.html", "deploy": "tsc --build --verbose && parcel build --public-url /wallet-adapter/example src/index.html", "clean": "shx mkdir -p .parcel-cache dist lib && shx rm -rf .parcel-cache dist lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "start": "parcel src/index.html"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@noble/curves": "^1.9.1", "@solana/wallet-adapter-base": "workspace:^", "@solana/wallet-adapter-react": "workspace:^", "@solana/wallet-adapter-react-ui": "workspace:^", "@solana/wallet-adapter-wallets": "workspace:^", "@solana/wallet-standard-features": "^1.3.0", "@solana/wallet-standard-util": "^1.1.2", "@solana/web3.js": "^1.98.2", "bs58": "^6.0.0", "next": "^15.3.2", "notistack": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node-fetch": "^2.6.12", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "parcel": "^2.15.2", "prettier": "^3.5.3", "process": "^0.11.10", "shx": "^0.4.0", "typescript": "^5.8.3"}}