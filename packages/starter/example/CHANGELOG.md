# @solana/wallet-adapter-example

## 0.18.41

### Patch Changes

- 75bf350: Update dependencies
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27
    - @solana/wallet-adapter-react@0.15.39
    - @solana/wallet-adapter-react-ui@0.9.39
    - @solana/wallet-adapter-wallets@0.19.37

## 0.18.40

### Patch Changes

- Updated dependencies [db923f1]
    - @solana/wallet-adapter-wallets@0.19.36
    - @solana/wallet-adapter-react-ui@0.9.38
    - @solana/wallet-adapter-react@0.15.38
    - @solana/wallet-adapter-base@0.9.26

## 0.18.39

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-wallets@0.19.35
    - @solana/wallet-adapter-react-ui@0.9.37
    - @solana/wallet-adapter-react@0.15.37
    - @solana/wallet-adapter-base@0.9.25

## 0.18.38

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.34

## 0.18.37

### Patch Changes

- c96cae47: The base version of Node has been raised to v20
- Updated dependencies [e25e7971]
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-react@0.15.36
    - @solana/wallet-adapter-wallets@0.19.33
    - @solana/wallet-adapter-material-ui@0.16.35
    - @solana/wallet-adapter-ant-design@0.11.33
    - @solana/wallet-adapter-react-ui@0.9.36
    - @solana/wallet-adapter-base@0.9.24

## 0.18.36

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.32

## 0.18.35

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.31

## 0.18.34

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.30

## 0.18.33

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.29

## 0.18.32

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.28

## 0.18.31

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.27

## 0.18.30

### Patch Changes

- Updated dependencies [cb29215]
    - @solana/wallet-adapter-material-ui@0.16.34
    - @solana/wallet-adapter-ant-design@0.11.32
    - @solana/wallet-adapter-react-ui@0.9.35
    - @solana/wallet-adapter-wallets@0.19.26

## 0.18.29

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.25

## 0.18.28

### Patch Changes

- Updated dependencies [46f06e72]
    - @solana/wallet-adapter-wallets@0.19.24

## 0.18.27

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.23

## 0.18.26

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.22

## 0.18.25

### Patch Changes

- Updated dependencies [bdc0eff]
    - @solana/wallet-adapter-wallets@0.19.21
    - @solana/wallet-adapter-react@0.15.35
    - @solana/wallet-adapter-ant-design@0.11.31
    - @solana/wallet-adapter-material-ui@0.16.33
    - @solana/wallet-adapter-react-ui@0.9.34

## 0.18.24

### Patch Changes

- a3d35a1: Add `signIn` (Sign In With Solana) method
- Updated dependencies [a3d35a1]
    - @solana/wallet-adapter-react@0.15.34
    - @solana/wallet-adapter-base@0.9.23
    - @solana/wallet-adapter-wallets@0.19.20
    - @solana/wallet-adapter-ant-design@0.11.30
    - @solana/wallet-adapter-material-ui@0.16.32
    - @solana/wallet-adapter-react-ui@0.9.33

## 0.18.23

### Patch Changes

- Updated dependencies [7b06737]
- Updated dependencies [ba57f75]
- Updated dependencies [7c6f2e1]
    - @solana/wallet-adapter-material-ui@0.16.31
    - @solana/wallet-adapter-ant-design@0.11.29
    - @solana/wallet-adapter-react-ui@0.9.32
    - @solana/wallet-adapter-react@0.15.33
    - @solana/wallet-adapter-wallets@0.19.19

## 0.18.22

### Patch Changes

- @solana/wallet-adapter-wallets@0.19.18

## 0.18.21

### Patch Changes

- 3b93799: Fix next.js warnings when building starter project

## 0.18.20

### Patch Changes

- 8a8fdc72: Update dependencies
- Updated dependencies [8a8fdc72]
    - @solana/wallet-adapter-wallets@0.19.15
    - @solana/wallet-adapter-material-ui@0.16.28
    - @solana/wallet-adapter-ant-design@0.11.26
    - @solana/wallet-adapter-react-ui@0.9.29
    - @solana/wallet-adapter-react@0.15.30
    - @solana/wallet-adapter-base@0.9.22

## 0.18.19

### Patch Changes

- f9e20fa0: Updated imports of Material UI icons in such a way that sidesteps the problem described here: https://github.com/mui/material-ui/issues/35233
- Updated dependencies [f9e20fa0]
    - @solana/wallet-adapter-material-ui@0.16.27

## 0.18.18

### Patch Changes

- e2a5b34: UI tweaks for wallet modal/dialogs
- Updated dependencies [e2a5b34]
    - @solana/wallet-adapter-ant-design@0.11.22
    - @solana/wallet-adapter-material-ui@0.16.23
    - @solana/wallet-adapter-react-ui@0.9.25

## 0.18.17

### Patch Changes

- 912cc0e: Allow wallets to customize autoConnect handling, adding support for Phantom deep links on iOS
- Updated dependencies [912cc0e]
    - @solana/wallet-adapter-base@0.9.20
    - @solana/wallet-adapter-react@0.15.26
    - @solana/wallet-adapter-react-ui@0.9.24
    - @solana/wallet-adapter-ant-design@0.11.21
    - @solana/wallet-adapter-material-ui@0.16.22
    - @solana/wallet-adapter-wallets@0.19.9

## 0.18.16

### Patch Changes

- 5d016a2: Mobile Wallet Adapter and Wallet Standard support in `@solana/wallet-adapter-react`

    - Early Access + Upgrade Guide: https://github.com/solana-labs/wallet-adapter/issues/604
    - Changes in this release: https://github.com/solana-labs/wallet-adapter/pull/598

- Updated dependencies [5d016a2]
    - @solana/wallet-adapter-react@0.15.22
    - @solana/wallet-adapter-ant-design@0.11.17
    - @solana/wallet-adapter-material-ui@0.16.18
    - @solana/wallet-adapter-react-ui@0.9.20
