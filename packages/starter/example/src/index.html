<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Wallet Adapter Example</title>
        <!-- Default styles that can be overridden by your app -->
        <link rel="stylesheet" href="npm:@solana/wallet-adapter-react-ui/styles.css" />
        <link rel="stylesheet" href="index.css" />
    </head>
    <body>
        <div id="app"></div>
        <script type="module" src="index.tsx"></script>
    </body>
</html>
