# @solana/wallet-adapter-solflare

## 0.6.32

### Patch Changes

- 4dad1a9: Update Solflare logo
- fe81e05: Update Solflare logo
- 75bf350: Update dependencies
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27

## 0.6.31

### Patch Changes

- db923f1: Use Node 20+ rather than 22
- Updated dependencies [db923f1]
    - @solana/wallet-adapter-base@0.9.26

## 0.6.30

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-base@0.9.25

## 0.6.29

### Patch Changes

- c96cae47: The base version of Node has been raised to v20
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-base@0.9.24

## 0.6.28

### Patch Changes

- 3d2e0cd5: Optimize Solflare MetaMask snap detection

## 0.6.27

### Patch Changes

- a4566f89: Add Solflare MetaMask Snap support

## 0.6.26

### Patch Changes

- Updated dependencies [a3d35a1]
    - @solana/wallet-adapter-base@0.9.23

## 0.6.25

### Patch Changes

- e5024dc: Support `signAndSendTransaction` method in Solflare adapter

## 0.6.24

### Patch Changes

- 8a8fdc72: Update dependencies
- Updated dependencies [8a8fdc72]
    - @solana/wallet-adapter-base@0.9.22

## 0.6.23

### Patch Changes

- Updated dependencies [f99c2154]
    - @solana/wallet-adapter-base@0.9.21

## 0.6.22

### Patch Changes

- b4558126: Add support for redirecting to Solflare browser on iOS

## 0.6.21

### Patch Changes

- 21d2c863: Add support for `accountChanged` event to Solflare adapter

## 0.6.20

### Patch Changes

- Updated dependencies [912cc0e]
    - @solana/wallet-adapter-base@0.9.20

## 0.6.19

### Patch Changes

- Updated dependencies [353f2a5]
    - @solana/wallet-adapter-base@0.9.19
