# @solana/wallet-adapter-trezor

## 0.1.6

### Patch Changes

- 75bf350: Allow app to configure appName in Trezor adapter
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27

## 0.1.5

### Patch Changes

- db923f1: Use Node 20+ rather than 22
- Updated dependencies [db923f1]
    - @solana/wallet-adapter-base@0.9.26

## 0.1.4

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-base@0.9.25

## 0.1.3

### Patch Changes

- 24780bcf: bump @trezor/connect-web to version 9.5.2
- e88d8749: bump @trezor/connect-web to version 9.5.0
- f3721882: Update Trezor icon
- c96cae47: The base version of Node has been raised to v20
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-base@0.9.24

## 0.1.2

### Patch Changes

- ba90b65: Fix Trezor adapter on Firefox browsers

## 0.1.1

### Patch Changes

- 375e548: bump @trezor/connect-web to version 9.2.1

## 0.1.0

### Minor Changes

- a9f41dde: Release Trezor adapter
