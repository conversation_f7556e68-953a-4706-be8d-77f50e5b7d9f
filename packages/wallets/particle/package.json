{"name": "@solana/wallet-adapter-particle", "version": "0.1.16", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["lib", "src", "LICENSE"], "engines": {"node": ">=20"}, "type": "module", "sideEffects": false, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"require": "./lib/cjs/index.js", "import": "./lib/esm/index.js", "types": "./lib/types/index.d.ts"}, "scripts": {"build": "tsc --build --verbose && pnpm run package", "clean": "shx mkdir -p lib && shx rm -rf lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "package": "shx mkdir -p lib/cjs && shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json"}, "peerDependencies": {"@solana/web3.js": "^1.98.0"}, "dependencies": {"@particle-network/solana-wallet": "^1.3.2", "@solana/wallet-adapter-base": "workspace:^"}, "devDependencies": {"@metamask/eth-sig-util": "^8.2.0", "@solana/web3.js": "^1.98.2", "shx": "^0.4.0"}}