# @solana/wallet-adapter-keystone

## 0.1.19

### Patch Changes

- 75bf350: Update dependencies
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27

## 0.1.18

### Patch Changes

- db923f1: Use Node 20+ rather than 22
- Updated dependencies [db923f1]
    - @solana/wallet-adapter-base@0.9.26

## 0.1.17

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-base@0.9.25

## 0.1.16

### Patch Changes

- c96cae47: The base version of Node has been raised to v20
- f326fb92: Add VersionedTransaction support to Keystone
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-base@0.9.24

## 0.1.15

### Patch Changes

- dc81bc9: Bump to ngraveio/bc-ur 1.1.12

## 0.1.14

### Patch Changes

- 84e2250: Add a resolution for ngraveio/bc-ur

## 0.1.13

### Patch Changes

- f535bac: Add a pnpm override for ngraveio/bc-ur

## 0.1.12

### Patch Changes

- Updated dependencies [a3d35a1]
    - @solana/wallet-adapter-base@0.9.23

## 0.1.11

### Patch Changes

- 8a8fdc72: Update dependencies
- Updated dependencies [8a8fdc72]
    - @solana/wallet-adapter-base@0.9.22

## 0.1.10

### Patch Changes

- Updated dependencies [f99c2154]
    - @solana/wallet-adapter-base@0.9.21

## 0.1.9

### Patch Changes

- Updated dependencies [912cc0e]
    - @solana/wallet-adapter-base@0.9.20

## 0.1.8

### Patch Changes

- Updated dependencies [353f2a5]
    - @solana/wallet-adapter-base@0.9.19
