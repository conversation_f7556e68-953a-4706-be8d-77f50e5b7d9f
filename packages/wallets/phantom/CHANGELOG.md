# @solana/wallet-adapter-phantom

## 0.9.28

### Patch Changes

- 75bf350: Update dependencies
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27

## 0.9.27

### Patch Changes

- db923f1: Use Node 20+ rather than 22
- Updated dependencies [db923f1]
    - @solana/wallet-adapter-base@0.9.26

## 0.9.26

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-base@0.9.25

## 0.9.25

### Patch Changes

- c96cae47: The base version of Node has been raised to v20
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-base@0.9.24

## 0.9.24

### Patch Changes

- Updated dependencies [a3d35a1]
    - @solana/wallet-adapter-base@0.9.23

## 0.9.23

### Patch Changes

- 4de663e: Phantom logo updated with rebrand (https://twitter.com/phantom/status/1674513272115175424)

## 0.9.22

### Patch Changes

- 8a8fdc72: Update dependencies
- Updated dependencies [8a8fdc72]
    - @solana/wallet-adapter-base@0.9.22

## 0.9.21

### Patch Changes

- f99c2154: Fix for Phantom adapter's `connected` state
- Updated dependencies [f99c2154]
    - @solana/wallet-adapter-base@0.9.21

## 0.9.20

### Patch Changes

- b4558126: Add support for redirecting to Solflare browser on iOS

## 0.9.19

### Patch Changes

- 912cc0e: Allow wallets to customize autoConnect handling, adding support for Phantom deep links on iOS
- Updated dependencies [912cc0e]
    - @solana/wallet-adapter-base@0.9.20

## 0.9.18

### Patch Changes

- fed93f5: Add support for VersionedTransaction to Phantom adapter
- Updated dependencies [353f2a5]
    - @solana/wallet-adapter-base@0.9.19
