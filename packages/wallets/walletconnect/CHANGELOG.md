# @solana/wallet-adapter-walletconnect

## 0.1.21

### Patch Changes

- 75bf350: Update dependencies
- Updated dependencies [75bf350]
    - @solana/wallet-adapter-base@0.9.27

## 0.1.20

### Patch Changes

- db923f1: Use Node 20+ rather than 22
- Updated dependencies [db923f1]
    - @solana/wallet-adapter-base@0.9.26

## 0.1.19

### Patch Changes

- 27e408d: Update dependencies
- Updated dependencies [27e408d]
    - @solana/wallet-adapter-base@0.9.25

## 0.1.18

### Patch Changes

- e6058c2f: Update WalletConnect implementation to use package from Reown: @walletconnect/solana-adapter@0.0.7

## 0.1.17

### Patch Changes

- c96cae47: The base version of Node has been raised to v20
- Updated dependencies [c96cae47]
    - @solana/wallet-adapter-base@0.9.24

## 0.1.16

### Patch Changes

- Updated dependencies [a3d35a1]
    - @solana/wallet-adapter-base@0.9.23

## 0.1.15

### Patch Changes

- 18e023f: Add support for versioned transactions

## 0.1.14

### Patch Changes

- 8a8fdc72: Update dependencies
- Updated dependencies [8a8fdc72]
    - @solana/wallet-adapter-base@0.9.22

## 0.1.13

### Patch Changes

- 61e86b58: Taking a new version of `@jnwng/walletconnect-solana` with upgraded dependencies on `@walletconnect/sign-client`

## 0.1.12

### Patch Changes

- f5abf15c: Using WalletConnect (alone, or through `@solana/wallet-adapter-wallets` no longer fatals in Next 13.

## 0.1.11

### Patch Changes

- Updated dependencies [f99c2154]
    - @solana/wallet-adapter-base@0.9.21

## 0.1.10

### Patch Changes

- Updated dependencies [912cc0e]
    - @solana/wallet-adapter-base@0.9.20

## 0.1.9

### Patch Changes

- Updated dependencies [353f2a5]
    - @solana/wallet-adapter-base@0.9.19
