{"name": "@solana/wallet-adapter-wallets", "version": "0.19.37", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["lib", "src", "LICENSE"], "engines": {"node": ">=20"}, "type": "module", "sideEffects": false, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"require": "./lib/cjs/index.js", "import": "./lib/esm/index.js", "types": "./lib/types/index.d.ts"}, "scripts": {"build": "tsc --build --verbose && pnpm run package", "clean": "shx mkdir -p lib && shx rm -rf lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "package": "shx mkdir -p lib/cjs && shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json"}, "peerDependencies": {"@solana/web3.js": "^1.98.0"}, "dependencies": {"@solana/wallet-adapter-alpha": "workspace:^", "@solana/wallet-adapter-avana": "workspace:^", "@solana/wallet-adapter-bitkeep": "workspace:^", "@solana/wallet-adapter-bitpie": "workspace:^", "@solana/wallet-adapter-clover": "workspace:^", "@solana/wallet-adapter-coin98": "workspace:^", "@solana/wallet-adapter-coinbase": "workspace:^", "@solana/wallet-adapter-coinhub": "workspace:^", "@solana/wallet-adapter-fractal": "workspace:^", "@solana/wallet-adapter-huobi": "workspace:^", "@solana/wallet-adapter-hyperpay": "workspace:^", "@solana/wallet-adapter-keystone": "workspace:^", "@solana/wallet-adapter-krystal": "workspace:^", "@solana/wallet-adapter-ledger": "workspace:^", "@solana/wallet-adapter-mathwallet": "workspace:^", "@solana/wallet-adapter-neko": "workspace:^", "@solana/wallet-adapter-nightly": "workspace:^", "@solana/wallet-adapter-nufi": "workspace:^", "@solana/wallet-adapter-onto": "workspace:^", "@solana/wallet-adapter-particle": "workspace:^", "@solana/wallet-adapter-phantom": "workspace:^", "@solana/wallet-adapter-safepal": "workspace:^", "@solana/wallet-adapter-saifu": "workspace:^", "@solana/wallet-adapter-salmon": "workspace:^", "@solana/wallet-adapter-sky": "workspace:^", "@solana/wallet-adapter-solflare": "workspace:^", "@solana/wallet-adapter-solong": "workspace:^", "@solana/wallet-adapter-spot": "workspace:^", "@solana/wallet-adapter-tokenary": "workspace:^", "@solana/wallet-adapter-tokenpocket": "workspace:^", "@solana/wallet-adapter-torus": "workspace:^", "@solana/wallet-adapter-trezor": "workspace:^", "@solana/wallet-adapter-trust": "workspace:^", "@solana/wallet-adapter-unsafe-burner": "workspace:^", "@solana/wallet-adapter-walletconnect": "workspace:^", "@solana/wallet-adapter-xdefi": "workspace:^"}, "devDependencies": {"@solana/web3.js": "^1.98.2", "shx": "^0.4.0"}}