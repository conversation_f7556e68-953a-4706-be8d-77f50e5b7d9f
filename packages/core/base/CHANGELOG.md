# @solana/wallet-adapter-base

## 0.9.27

### Patch Changes

- 75bf350: Update dependencies

## 0.9.26

### Patch Changes

- db923f1: Use Node 20+ rather than 22

## 0.9.25

### Patch Changes

- 27e408d: Update dependencies

## 0.9.24

### Patch Changes

- c96cae47: The base version of Node has been raised to v20

## 0.9.23

### Patch Changes

- a3d35a1: Add `signIn` (Sign In With Solana) method

## 0.9.22

### Patch Changes

- 8a8fdc72: Update dependencies

## 0.9.21

### Patch Changes

- f99c2154: Add StandardAdapter to base Adapter type

## 0.9.20

### Patch Changes

- 912cc0e: Allow wallets to customize autoConnect handling, adding support for Phantom deep links on iOS

## 0.9.19

### Patch Changes

- 353f2a5: Add isVersionedTransaction helper function
