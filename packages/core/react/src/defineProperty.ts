type InferValue<Prop extends PropertyKey, Desc> = Des<PERSON> extends { get(): any; value: any }
    ? never
    : <PERSON><PERSON> extends { value: infer T }
      ? Record<Prop, T>
      : <PERSON><PERSON> extends { get(): infer T }
        ? Record<Prop, T>
        : never;

type DefineProperty<Prop extends PropertyKey, <PERSON><PERSON> extends PropertyDescriptor> = Desc extends {
    writable: any;
    set(val: any): any;
}
    ? never
    : <PERSON><PERSON> extends { writable: any; get(): any }
      ? never
      : <PERSON><PERSON> extends { writable: false }
        ? Readonly<InferValue<Prop, Desc>>
        : Des<PERSON> extends { writable: true }
          ? InferValue<Prop, Desc>
          : Readonly<InferValue<Prop, Desc>>;

export default function defineProperty<Obj extends object, Key extends PropertyKey, <PERSON>esc extends PropertyDescriptor>(
    obj: Obj,
    prop: Key,
    val: PDesc
): asserts obj is Obj & DefineProperty<Key, PDesc> {
    Object.defineProperty(obj, prop, val);
}
