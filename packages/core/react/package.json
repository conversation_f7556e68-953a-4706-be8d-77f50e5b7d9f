{"name": "@solana/wallet-adapter-react", "version": "0.15.39", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["lib", "src", "LICENSE"], "engines": {"node": ">=20"}, "type": "module", "sideEffects": false, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"require": "./lib/cjs/index.js", "import": "./lib/esm/index.js", "types": "./lib/types/index.d.ts"}, "scripts": {"build": "tsc --build --verbose && pnpm run package", "clean": "shx mkdir -p lib && shx rm -rf lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "package": "shx mkdir -p lib/cjs && shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json", "test": "jest"}, "peerDependencies": {"@solana/web3.js": "^1.98.0", "react": "*"}, "dependencies": {"@solana-mobile/wallet-adapter-mobile": "^2.2.0", "@solana/wallet-adapter-base": "workspace:^", "@solana/wallet-standard-wallet-adapter-react": "^1.1.4"}, "devDependencies": {"@solana/web3.js": "^1.98.2", "@types/jest": "^29.5.14", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-localstorage-mock": "^2.4.26", "react": "^19.1.0", "react-dom": "^19.1.0", "shx": "^0.4.0", "ts-jest": "^29.3.4"}}