{"private": true, "name": "@solana/wallet-adapter", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/anza-xyz/wallet-adapter", "license": "Apache-2.0", "engines": {"node": ">=20", "pnpm": "10.12.1"}, "packageManager": "pnpm@10.12.1", "type": "module", "sideEffects": false, "scripts": {"corepack": "corepack enable && corepack prepare pnpm@10.12.1 --activate && corepack use pnpm@10.12.1", "nuke": "shx rm -rf packages/*/*/node_modules node_modules pnpm-lock.yaml || true", "reinstall": "pnpm run nuke && pnpm install", "clean": "pnpm --recursive --workspace-concurrency=0 run clean && shx rm -rf **/*.tsbuildinfo", "build": "turbo run build --concurrency=100%", "build:clean": "pnpm run clean && pnpm run build", "release": "pnpm run build:clean && pnpm test && changeset publish && git push --follow-tags && git status", "watch": "tsc --build --verbose --watch tsconfig.all.json", "fmt": "prettier --write '{*,**/*}.{ts,tsx,js,jsx,json}'", "lint": "turbo run lint --concurrency=100%", "lint:fix": "pnpm run fmt && eslint --fix .", "test": "turbo run test --concurrency=100%", "deploy": "pnpm run deploy:docs && pnpm run deploy:example", "docs": "shx rm -rf docs && NODE_OPTIONS=--max_old_space_size=16000 typedoc && shx cp ./{.nojekyll,wallets.png} docs/", "deploy:docs": "pnpm run docs && gh-pages --dist docs --dotfiles", "example": "pnpm run --filter {packages/starter/example} deploy", "deploy:example": "pnpm run example && gh-pages --dist packages/starter/example/dist --dest example --dotfiles"}, "devDependencies": {"@changesets/cli": "^2.29.4", "@parcel/resolver-default": "2.15.2", "@types/node": "^22.15.24", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "8.22.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-require-extensions": "^0.1.3", "gh-pages": "^6.3.0", "pnpm": "10.12.1", "prettier": "^3.5.3", "shx": "^0.4.0", "turbo": "^2.5.3", "typedoc": "^0.28.5", "typescript": "^5.8.3", "vm-browserify": "^1.1.2"}, "overrides": {"@solana/wallet-adapter-base": "workspace:^", "eslint": "8.22.0", "@ngraveio/bc-ur": "1.1.12", "@types/web": "npm:typescript@^5.8.3"}, "resolutions": {"@solana/wallet-adapter-base": "workspace:^", "eslint": "8.22.0", "@ngraveio/bc-ur": "1.1.12", "@types/web": "npm:typescript@^5.8.3"}, "@parcel/resolver-default": {"packageExports": true}}