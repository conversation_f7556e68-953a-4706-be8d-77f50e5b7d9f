# Wallet Adapter

Modular TypeScript wallet adapters and components for Solana applications.

- [Demo](https://anza-xyz.github.io/wallet-adapter/example)
- [TypeScript Docs](https://anza-xyz.github.io/wallet-adapter/)
- [For Solana Apps](https://github.com/anza-xyz/wallet-adapter/blob/master/APP.md)
- [For Solana Wallets](https://github.com/anza-xyz/wallet-adapter/blob/master/WALLET.md)
- [Packages](https://github.com/anza-xyz/wallet-adapter/blob/master/PACKAGES.md)
- [FAQ (Frequently Asked Questions)](https://github.com/anza-xyz/wallet-adapter/blob/master/FAQ.md)
- [Build from Source](https://github.com/anza-xyz/wallet-adapter/blob/master/BUILD.md)


![Wallets](wallets.png)
