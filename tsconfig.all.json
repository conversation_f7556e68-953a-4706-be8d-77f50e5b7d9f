{"extends": "./tsconfig.root.json", "references": [{"path": "./packages/core/base/tsconfig.json"}, {"path": "./packages/core/react/tsconfig.json"}, {"path": "./packages/starter/example/tsconfig.json"}, {"path": "./packages/starter/react-ui-starter/tsconfig.json"}, {"path": "./packages/ui/base-ui/tsconfig.json"}, {"path": "./packages/ui/react-ui/tsconfig.json"}, {"path": "./packages/wallets/alpha/tsconfig.json"}, {"path": "./packages/wallets/avana/tsconfig.json"}, {"path": "./packages/wallets/bitkeep/tsconfig.json"}, {"path": "./packages/wallets/bitpie/tsconfig.json"}, {"path": "./packages/wallets/clover/tsconfig.json"}, {"path": "./packages/wallets/coin98/tsconfig.json"}, {"path": "./packages/wallets/coinbase/tsconfig.json"}, {"path": "./packages/wallets/coinhub/tsconfig.json"}, {"path": "./packages/wallets/fractal/tsconfig.json"}, {"path": "./packages/wallets/huobi/tsconfig.json"}, {"path": "./packages/wallets/hyperpay/tsconfig.json"}, {"path": "./packages/wallets/keystone/tsconfig.json"}, {"path": "./packages/wallets/krystal/tsconfig.json"}, {"path": "./packages/wallets/ledger/tsconfig.json"}, {"path": "./packages/wallets/mathwallet/tsconfig.json"}, {"path": "./packages/wallets/neko/tsconfig.json"}, {"path": "./packages/wallets/nightly/tsconfig.json"}, {"path": "./packages/wallets/nufi/tsconfig.json"}, {"path": "./packages/wallets/onto/tsconfig.json"}, {"path": "./packages/wallets/particle/tsconfig.json"}, {"path": "./packages/wallets/phantom/tsconfig.json"}, {"path": "./packages/wallets/safepal/tsconfig.json"}, {"path": "./packages/wallets/saifu/tsconfig.json"}, {"path": "./packages/wallets/salmon/tsconfig.json"}, {"path": "./packages/wallets/sky/tsconfig.json"}, {"path": "./packages/wallets/solflare/tsconfig.json"}, {"path": "./packages/wallets/solong/tsconfig.json"}, {"path": "./packages/wallets/spot/tsconfig.json"}, {"path": "./packages/wallets/tokenary/tsconfig.json"}, {"path": "./packages/wallets/tokenpocket/tsconfig.json"}, {"path": "./packages/wallets/torus/tsconfig.json"}, {"path": "./packages/wallets/trezor/tsconfig.json"}, {"path": "./packages/wallets/trust/tsconfig.json"}, {"path": "./packages/wallets/unsafe-burner/tsconfig.json"}, {"path": "./packages/wallets/walletconnect/tsconfig.json"}, {"path": "./packages/wallets/wallets/tsconfig.json"}, {"path": "./packages/wallets/xdefi/tsconfig.json"}]}