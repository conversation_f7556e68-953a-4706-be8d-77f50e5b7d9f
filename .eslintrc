{"root": true, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:require-extensions/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier", "react", "react-hooks", "require-extensions"], "settings": {"react": {"version": "detect"}}, "rules": {"@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/consistent-type-imports": "error", "react/no-unescaped-entities": ["error", {"forbid": [">"]}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "overrides": [{"files": ["packages/starter/**/*"], "rules": {"require-extensions/require-extensions": "off"}}]}